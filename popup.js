/**
 * 实时指令插件 - Popup界面脚本
 * 负责处理用户输入、语音输入和与content script的通信
 * 支持Win+H语音输入和浏览器内置语音识别功能
 */

// DOM元素引用
let commandInput;
let sendButton;
let clearButton;
let statusText;
let voiceIndicator;

// 状态管理
let isConnected = false;
let lastCommand = '';
let sendTimeout = null;

// 语音输入相关状态
let isVoiceInputActive = false;
let speechRecognition = null;
let voiceInputTimeout = null;
let lastInputLength = 0;

// 智能语音快捷指令相关状态
let shortcutCommands = new Map();
let commandUsageStats = new Map();
let isShortcutManagerVisible = false;

// 语音清空功能相关状态
let clearCommandPatterns = {
    // 插件输入框清空指令
    plugin: {
        chinese: ['清空输入框', '清除指令', '重置输入', '清空命令框', '清空插件', '清空弹窗', '清空popup'],
        english: ['clear input', 'reset command', 'clear box', 'clear plugin', 'reset input'],
        keywords: ['输入框', '指令', '命令框', '插件', '弹窗', 'popup', 'input', 'command']
    },
    // 网页输入框清空指令
    page: {
        chinese: ['清空页面输入框', '清除网页内容', '删除表单内容', '清空网页', '清空表单', '清空网站'],
        english: ['clear page input', 'clear form', 'delete page content', 'clear website', 'clear page'],
        keywords: ['页面', '网页', '表单', '网站', '浏览器', '当前页', 'page', 'form', 'website']
    },
    // 全部清空指令
    all: {
        chinese: ['全部清空', '清空所有', '重置全部', '清空一切'],
        english: ['clear all', 'reset everything', 'delete all', 'clear everything'],
        keywords: ['全部', '所有', '一切', 'everything', 'all']
    }
};

// 排除的非清空指令（防止误触发）
let excludedClearPatterns = [
    '清空购物车', '清空回收站', '清空缓存', '清空历史', '清空下载',
    'clear cart', 'clear trash', 'clear cache', 'clear history', 'clear downloads'
];

/**
 * 初始化函数
 */
async function initialize() {
    console.log('🚀 Popup界面初始化开始...');

    // 获取DOM元素
    commandInput = document.getElementById('commandInput');
    sendButton = document.getElementById('sendButton');
    clearButton = document.getElementById('clearButton');
    statusText = document.getElementById('statusText');
    voiceIndicator = document.getElementById('voiceIndicator');

    // 验证DOM元素
    if (!commandInput || !sendButton || !clearButton || !statusText) {
        console.error('❌ 无法找到必要的DOM元素');
        updateStatus('初始化失败：DOM元素缺失', 'error');
        return;
    }

    // 初始化语音识别功能
    initializeSpeechRecognition();

    // 初始化智能语音快捷指令系统
    await initializeShortcutCommands();

    // 绑定事件监听器
    setupEventListeners();

    // 检查连接状态
    checkConnection();

    // 聚焦到输入框
    commandInput.focus();

    console.log('✅ Popup界面初始化完成');
    console.log('🎤 语音输入支持状态:', speechRecognition ? '已启用' : '不支持');
}

/**
 * 初始化语音识别功能
 * 支持浏览器内置的Web Speech API
 */
function initializeSpeechRecognition() {
    try {
        // 检查浏览器是否支持语音识别
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        if (!SpeechRecognition) {
            console.warn('⚠️ 浏览器不支持Web Speech API语音识别');
            if (voiceIndicator) {
                voiceIndicator.style.opacity = '0.3';
                voiceIndicator.title = '浏览器不支持语音识别，请使用Win+H系统语音输入';
            }
            return;
        }

        // 创建语音识别实例
        speechRecognition = new SpeechRecognition();

        // 配置语音识别参数
        speechRecognition.continuous = false;          // 不连续识别
        speechRecognition.interimResults = true;       // 显示临时结果
        speechRecognition.lang = 'zh-CN';              // 中文识别
        speechRecognition.maxAlternatives = 1;         // 最多1个候选结果

        // 语音识别开始事件
        speechRecognition.onstart = () => {
            console.log('🎤 语音识别开始');
            isVoiceInputActive = true;
            updateVoiceIndicator(true);
            updateStatus('正在监听语音输入...', 'success');
        };

        // 语音识别结果事件
        speechRecognition.onresult = (event) => {
            let transcript = '';

            // 获取识别结果
            for (let i = event.resultIndex; i < event.results.length; i++) {
                if (event.results[i].isFinal) {
                    transcript += event.results[i][0].transcript;
                } else {
                    // 临时结果，实时显示
                    const interimTranscript = event.results[i][0].transcript;
                    commandInput.value = lastCommand + interimTranscript;
                }
            }

            // 如果有最终结果，处理语音指令
            if (transcript) {
                const trimmedTranscript = transcript.trim();
                console.log('🎤 语音识别结果:', trimmedTranscript);

                // 优先检查是否为清空指令
                const clearMatch = detectClearCommand(trimmedTranscript);

                if (clearMatch) {
                    console.log('🧹 语音识别到清空指令，立即执行:', clearMatch);
                    // 立即执行清空指令，不更新输入框
                    executeClearCommand(clearMatch).catch(error => {
                        console.error('❌ 语音清空指令执行失败:', error);
                    });
                    return;
                }

                // 如果不是清空指令，正常更新输入框
                commandInput.value = trimmedTranscript;

                // 触发input事件以启动实时传递
                const inputEvent = new Event('input', { bubbles: true });
                commandInput.dispatchEvent(inputEvent);
            }
        };

        // 语音识别结束事件
        speechRecognition.onend = () => {
            console.log('🎤 语音识别结束');
            isVoiceInputActive = false;
            updateVoiceIndicator(false);
            updateStatus('语音输入完成', 'success');
        };

        // 语音识别错误事件
        speechRecognition.onerror = (event) => {
            console.error('❌ 语音识别错误:', event.error);
            isVoiceInputActive = false;
            updateVoiceIndicator(false);

            let errorMessage = '语音识别失败';
            switch (event.error) {
                case 'no-speech':
                    errorMessage = '未检测到语音输入';
                    break;
                case 'audio-capture':
                    errorMessage = '无法访问麦克风';
                    break;
                case 'not-allowed':
                    errorMessage = '麦克风权限被拒绝';
                    break;
                case 'network':
                    errorMessage = '网络连接错误';
                    break;
                default:
                    errorMessage = `语音识别错误: ${event.error}`;
            }

            updateStatus(errorMessage, 'error');
        };

        console.log('✅ 语音识别功能初始化成功');

    } catch (error) {
        console.error('❌ 语音识别初始化失败:', error);
        if (voiceIndicator) {
            voiceIndicator.style.opacity = '0.3';
            voiceIndicator.title = '语音识别初始化失败，请使用Win+H系统语音输入';
        }
    }
}

/**
 * 更新语音指示器状态
 */
function updateVoiceIndicator(isActive) {
    if (!voiceIndicator) return;

    if (isActive) {
        voiceIndicator.classList.add('active');
        voiceIndicator.textContent = '🔴'; // 录音中
    } else {
        voiceIndicator.classList.remove('active');
        voiceIndicator.textContent = '🎤'; // 待机状态
    }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 实时输入监听（input事件 - 支持语音输入）
    commandInput.addEventListener('input', handleInputChange);

    // 键盘事件监听
    commandInput.addEventListener('keydown', handleKeyDown);

    // 手动发送按钮
    sendButton.addEventListener('click', handleManualSend);

    // 清空按钮
    clearButton.addEventListener('click', handleClear);

    // 语音指示器点击事件
    if (voiceIndicator) {
        voiceIndicator.addEventListener('click', handleVoiceInputToggle);
    }

    // 快捷指令管理按钮事件
    setupShortcutManagerEvents();

    // 输入框焦点事件
    commandInput.addEventListener('focus', () => {
        updateStatus('输入框已激活，支持键盘和语音输入...', 'success');
    });

    // 输入框失焦事件
    commandInput.addEventListener('blur', () => {
        // 如果正在进行语音输入，不要停止
        if (!isVoiceInputActive) {
            updateStatus('输入框失去焦点', '');
        }
    });

    // 监听Win+H等系统语音输入的变化
    // 通过监听输入框长度变化来检测语音输入
    setInterval(detectVoiceInput, 100);

    console.log('📡 事件监听器设置完成');
    console.log('🎤 语音输入监听已启动');
}

/**
 * 处理语音输入切换
 */
function handleVoiceInputToggle() {
    if (!speechRecognition) {
        updateStatus('浏览器不支持语音识别，请使用Win+H系统语音输入', 'error');
        return;
    }

    try {
        if (isVoiceInputActive) {
            // 停止语音识别
            speechRecognition.stop();
            console.log('🎤 手动停止语音识别');
        } else {
            // 开始语音识别
            speechRecognition.start();
            console.log('🎤 手动开始语音识别');
        }
    } catch (error) {
        console.error('❌ 语音识别操作失败:', error);
        updateStatus('语音识别操作失败', 'error');
    }
}

/**
 * 检测系统语音输入（如Win+H）
 * 通过监听输入框内容长度变化来检测
 */
function detectVoiceInput() {
    if (!commandInput) return;

    const currentLength = commandInput.value.length;

    // 检测到内容突然增加（可能是语音输入）
    if (currentLength > lastInputLength + 1 && !isVoiceInputActive) {
        console.log('🎤 检测到可能的系统语音输入');
        updateStatus('检测到语音输入，正在处理...', 'success');

        // 模拟语音输入状态
        updateVoiceIndicator(true);

        // 延迟恢复状态
        if (voiceInputTimeout) {
            clearTimeout(voiceInputTimeout);
        }

        voiceInputTimeout = setTimeout(() => {
            updateVoiceIndicator(false);
            updateStatus('语音输入处理完成', 'success');
        }, 1500);
    }

    lastInputLength = currentLength;
}

/**
 * 处理输入变化（实时传递）
 * 优化支持语音输入场景
 */
function handleInputChange(event) {
    const command = event.target.value.trim();

    console.log('📝 输入变化:', {
        command: command,
        length: command.length,
        isVoiceActive: isVoiceInputActive,
        lastCommand: lastCommand
    });

    // 防抖处理：避免过于频繁的发送
    // 语音输入时使用较短的延迟，键盘输入使用正常延迟
    const debounceDelay = isVoiceInputActive ? 150 : 300;

    if (sendTimeout) {
        clearTimeout(sendTimeout);
    }

    sendTimeout = setTimeout(async () => {
        // 避免重复发送相同指令
        if (command !== lastCommand) {
            lastCommand = command;

            if (command.length > 0) {
                try {
                    // 1. 优先检查是否为清空指令
                    const clearMatch = detectClearCommand(command);

                    if (clearMatch) {
                        console.log('🧹 检测到清空指令，执行清空操作:', clearMatch);
                        await executeClearCommand(clearMatch);
                        return;
                    }

                    // 2. 检查是否为快捷指令
                    const shortcutMatch = matchShortcutCommand(command);

                    if (shortcutMatch) {
                        // 执行快捷指令
                        console.log('🎯 检测到快捷指令，执行快捷指令:', shortcutMatch);
                        await executeShortcutCommand(shortcutMatch);

                        // 清空输入框（可选）
                        // 注意：实时输入时不清空，避免影响用户体验

                        return;
                    }

                    // 3. 如果不是特殊指令，按原有逻辑处理
                    await sendCommandToContent(command, true); // true表示实时发送

                    // 根据输入方式显示不同的状态信息
                    const inputMethod = isVoiceInputActive ? '语音' : '键盘';
                    updateStatus(`${inputMethod}实时发送: "${command}"`, 'success');

                    console.log(`✅ ${inputMethod}指令发送成功:`, command);

                } catch (error) {
                    console.error('❌ 实时指令发送失败:', error);
                    updateStatus(`发送失败: ${error.message}`, 'error');
                }
            } else {
                updateStatus('输入为空，等待指令...', '');
            }
        }
    }, debounceDelay);
}

/**
 * 处理键盘事件
 */
function handleKeyDown(event) {
    // Enter键手动发送
    if (event.key === 'Enter') {
        event.preventDefault();
        handleManualSend();
    }

    // Escape键清空
    if (event.key === 'Escape') {
        event.preventDefault();
        handleClear();
    }
}

/**
 * 处理手动发送
 */
async function handleManualSend() {
    const command = commandInput.value.trim();

    if (command.length === 0) {
        updateStatus('请输入指令内容', 'error');
        commandInput.focus();
        return;
    }

    try {
        // 1. 优先检查是否为清空指令
        const clearMatch = detectClearCommand(command);

        if (clearMatch) {
            console.log('🧹 手动执行清空指令:', clearMatch);
            await executeClearCommand(clearMatch);
            return;
        }

        // 2. 检查是否为快捷指令
        const shortcutMatch = matchShortcutCommand(command);

        if (shortcutMatch) {
            // 执行快捷指令
            console.log('🎯 手动执行快捷指令:', shortcutMatch);
            await executeShortcutCommand(shortcutMatch);

            // 清空输入框
            commandInput.value = '';
            lastCommand = '';

            return;
        }

        // 3. 如果不是特殊指令，按原有逻辑处理
        await sendCommandToContent(command, false); // false表示手动发送
        updateStatus(`手动发送: "${command}"`, 'success');

        console.log('📤 手动发送指令:', command);

    } catch (error) {
        console.error('❌ 手动发送失败:', error);
        updateStatus(`发送失败: ${error.message}`, 'error');
    }
}

/**
 * 处理清空操作
 */
function handleClear() {
    commandInput.value = '';
    lastCommand = '';
    updateStatus('已清空输入内容', '');
    commandInput.focus();

    // 发送清空指令到content script
    sendCommandToContent('', false, 'clear');

    console.log('🗑️ 已清空输入内容');
}

/**
 * 发送指令到content script
 * 支持语音输入和实时指令传递的优化版本
 */
async function sendCommandToContent(command, isRealtime = false, action = 'execute') {
    try {
        // 获取当前活动标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        if (!tab) {
            throw new Error('无法获取当前标签页 - 请确保有活动的网页标签');
        }

        if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
            throw new Error('无法在Chrome内部页面上执行指令');
        }

        // 构造增强的消息对象
        const message = {
            type: 'COMMAND_FROM_POPUP',
            command: command,
            isRealtime: isRealtime,
            action: action,
            timestamp: Date.now(),
            tabId: tab.id,
            tabUrl: tab.url,
            tabTitle: tab.title,
            inputMethod: isVoiceInputActive ? 'voice' : 'keyboard',
            version: '1.0.0'
        };

        console.log('📤 发送指令到content script:', {
            command: command,
            isRealtime: isRealtime,
            action: action,
            inputMethod: message.inputMethod,
            tabUrl: tab.url
        });

        // 发送消息到content script
        const response = await chrome.tabs.sendMessage(tab.id, message);

        if (response && response.success) {
            isConnected = true;
            console.log('✅ 指令发送成功:', {
                command: command,
                response: response.message,
                timestamp: message.timestamp
            });

            // 只在手动发送时显示成功状态
            if (!isRealtime) {
                const inputMethod = isVoiceInputActive ? '语音' : '手动';
                updateStatus(`${inputMethod}指令执行成功: "${command}"`, 'success');
            }

            return response;

        } else {
            throw new Error(response?.error || 'Content script响应异常');
        }

    } catch (error) {
        isConnected = false;

        // 详细的错误日志
        console.error('❌ 发送指令失败:', {
            error: error.message,
            command: command,
            isRealtime: isRealtime,
            action: action,
            timestamp: Date.now()
        });

        // 根据错误类型提供不同的处理方案
        let errorMessage = error.message;
        let shouldRetry = false;

        if (error.message.includes('Could not establish connection')) {
            errorMessage = 'Content script未加载，正在尝试重新注入...';
            shouldRetry = true;
        } else if (error.message.includes('Receiving end does not exist')) {
            errorMessage = '网页未准备就绪，正在重新连接...';
            shouldRetry = true;
        } else if (error.message.includes('chrome://')) {
            errorMessage = '无法在Chrome内部页面执行指令';
        }

        updateStatus(`发送失败: ${errorMessage}`, 'error');

        // 如果是连接错误，尝试重新注入content script
        if (shouldRetry) {
            await injectContentScript();
        }

        throw error; // 重新抛出错误供调用者处理
    }
}

/**
 * 注入content script（备用方案）
 */
async function injectContentScript() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        if (!tab) {
            throw new Error('无法获取当前标签页');
        }

        // 注入content script
        await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
        });

        updateStatus('Content script已重新注入', 'success');
        console.log('🔄 Content script重新注入成功');

    } catch (error) {
        console.error('❌ 注入content script失败:', error);
        updateStatus('无法连接到网页，请刷新页面后重试', 'error');
    }
}

/**
 * 检查连接状态
 */
async function checkConnection() {
    try {
        await sendCommandToContent('ping', false, 'ping');
        updateStatus('连接正常，可以开始使用', 'success');
    } catch (error) {
        updateStatus('正在建立连接...', '');
        // 尝试注入content script
        await injectContentScript();
    }
}

/**
 * 更新状态显示
 */
function updateStatus(message, type = '') {
    if (!statusText) return;

    statusText.textContent = message;
    statusText.className = `status-text ${type}`;

    console.log(`📊 状态更新: ${message} (${type})`);
}

/**
 * 页面加载完成后初始化
 */
document.addEventListener('DOMContentLoaded', initialize);

/**
 * 错误处理
 */
window.addEventListener('error', (event) => {
    console.error('❌ Popup脚本错误:', event.error);
    updateStatus('插件运行出错，请重新打开', 'error');
});

/**
 * ========================================
 * 智能语音快捷指令系统
 * ========================================
 */

/**
 * 初始化智能语音快捷指令系统
 */
async function initializeShortcutCommands() {
    console.log('🚀 初始化智能语音快捷指令系统...');

    try {
        // 加载存储的快捷指令
        await loadShortcutCommands();

        // 加载使用统计
        await loadUsageStats();

        // 初始化默认快捷指令（如果是首次使用）
        await initializeDefaultShortcuts();

        console.log('✅ 智能语音快捷指令系统初始化完成');
        console.log('📊 已加载快捷指令数量:', shortcutCommands.size);

    } catch (error) {
        console.error('❌ 智能语音快捷指令系统初始化失败:', error);
        updateStatus('快捷指令系统初始化失败', 'error');
    }
}

/**
 * 加载存储的快捷指令
 */
async function loadShortcutCommands() {
    try {
        const result = await chrome.storage.sync.get(['shortcutCommands']);

        if (result.shortcutCommands) {
            // 将存储的对象转换为Map
            shortcutCommands = new Map(Object.entries(result.shortcutCommands));
            console.log('📥 已加载存储的快捷指令:', shortcutCommands);
        } else {
            console.log('📝 未找到存储的快捷指令，将使用默认配置');
        }

    } catch (error) {
        console.error('❌ 加载快捷指令失败:', error);
        throw error;
    }
}

/**
 * 保存快捷指令到存储
 */
async function saveShortcutCommands() {
    try {
        // 将Map转换为普通对象进行存储
        const commandsObject = Object.fromEntries(shortcutCommands);

        await chrome.storage.sync.set({
            shortcutCommands: commandsObject
        });

        console.log('💾 快捷指令已保存到存储');

    } catch (error) {
        console.error('❌ 保存快捷指令失败:', error);
        throw error;
    }
}

/**
 * 加载使用统计
 */
async function loadUsageStats() {
    try {
        const result = await chrome.storage.sync.get(['commandUsageStats']);

        if (result.commandUsageStats) {
            commandUsageStats = new Map(Object.entries(result.commandUsageStats));
            console.log('📊 已加载使用统计:', commandUsageStats);
        }

    } catch (error) {
        console.error('❌ 加载使用统计失败:', error);
    }
}

/**
 * 保存使用统计
 */
async function saveUsageStats() {
    try {
        const statsObject = Object.fromEntries(commandUsageStats);

        await chrome.storage.sync.set({
            commandUsageStats: statsObject
        });

        console.log('📊 使用统计已保存');

    } catch (error) {
        console.error('❌ 保存使用统计失败:', error);
    }
}

/**
 * 初始化默认快捷指令
 */
async function initializeDefaultShortcuts() {
    // 如果已有快捷指令，跳过初始化
    if (shortcutCommands.size > 0) {
        return;
    }

    console.log('🔧 初始化默认快捷指令...');

    // 默认快捷指令配置
    const defaultShortcuts = {
        // 邮箱相关
        '打开我的邮箱': 'https://mail.google.com',
        '进入邮箱': 'https://mail.google.com',
        '查看邮件': 'https://mail.google.com',
        '打开Gmail': 'https://mail.google.com',

        // 购物相关
        '进入我的购物车': 'https://www.taobao.com/cart',
        '打开购物车': 'https://www.taobao.com/cart',
        '去淘宝': 'https://www.taobao.com',
        '打开京东': 'https://www.jd.com',

        // 社交媒体
        '打开微博': 'https://weibo.com',
        '进入微信网页版': 'https://wx.qq.com',
        '打开知乎': 'https://www.zhihu.com',
        '去B站': 'https://www.bilibili.com',

        // 工作相关
        '我的工作台': 'about:blank', // 用户可自定义
        '打开GitHub': 'https://github.com',
        '进入云文档': 'https://docs.qq.com',

        // 搜索引擎
        '百度搜索': 'https://www.baidu.com',
        '谷歌搜索': 'https://www.google.com',

        // 视频娱乐
        '看视频': 'https://www.bilibili.com',
        '打开优酷': 'https://www.youku.com',
        '进入爱奇艺': 'https://www.iqiyi.com',

        // 新闻资讯
        '看新闻': 'https://news.baidu.com',
        '打开今日头条': 'https://www.toutiao.com'
    };

    // 添加到快捷指令Map
    for (const [command, url] of Object.entries(defaultShortcuts)) {
        shortcutCommands.set(command, {
            url: url,
            description: `打开 ${url}`,
            isDefault: true,
            createdAt: Date.now()
        });
    }

    // 保存到存储
    await saveShortcutCommands();

    console.log('✅ 默认快捷指令初始化完成，共', Object.keys(defaultShortcuts).length, '条');
}

/**
 * ========================================
 * 语音清空功能系统
 * ========================================
 */

/**
 * 检测清空指令
 * @param {string} inputCommand - 输入的指令
 * @returns {Object|null} - 清空指令信息或null
 */
function detectClearCommand(inputCommand) {
    console.log('🔍 检测清空指令:', inputCommand);

    const normalizedInput = inputCommand.trim().toLowerCase();

    // 检查是否为排除的指令（防止误触发）
    for (const excluded of excludedClearPatterns) {
        if (normalizedInput.includes(excluded.toLowerCase())) {
            console.log('❌ 排除的清空指令:', excluded);
            return null;
        }
    }

    // 检查是否包含清空相关的基础词汇
    const clearKeywords = ['清空', '清除', '重置', '删除', 'clear', 'reset', 'delete'];
    const hasClearKeyword = clearKeywords.some(keyword =>
        normalizedInput.includes(keyword.toLowerCase())
    );

    if (!hasClearKeyword) {
        return null;
    }

    console.log('✅ 检测到清空关键词');

    // 1. 精确匹配全部清空指令
    for (const pattern of clearCommandPatterns.all.chinese.concat(clearCommandPatterns.all.english)) {
        if (normalizedInput === pattern.toLowerCase()) {
            console.log('✅ 精确匹配全部清空指令:', pattern);
            return { type: 'all', pattern, confidence: 1.0, method: 'exact' };
        }
    }

    // 2. 精确匹配插件清空指令
    for (const pattern of clearCommandPatterns.plugin.chinese.concat(clearCommandPatterns.plugin.english)) {
        if (normalizedInput === pattern.toLowerCase()) {
            console.log('✅ 精确匹配插件清空指令:', pattern);
            return { type: 'plugin', pattern, confidence: 1.0, method: 'exact' };
        }
    }

    // 3. 精确匹配页面清空指令
    for (const pattern of clearCommandPatterns.page.chinese.concat(clearCommandPatterns.page.english)) {
        if (normalizedInput === pattern.toLowerCase()) {
            console.log('✅ 精确匹配页面清空指令:', pattern);
            return { type: 'page', pattern, confidence: 1.0, method: 'exact' };
        }
    }

    // 4. 关键词匹配
    let scores = { all: 0, plugin: 0, page: 0 };

    // 检查全部清空关键词
    for (const keyword of clearCommandPatterns.all.keywords) {
        if (normalizedInput.includes(keyword.toLowerCase())) {
            scores.all += 1;
        }
    }

    // 检查插件清空关键词
    for (const keyword of clearCommandPatterns.plugin.keywords) {
        if (normalizedInput.includes(keyword.toLowerCase())) {
            scores.plugin += 1;
        }
    }

    // 检查页面清空关键词
    for (const keyword of clearCommandPatterns.page.keywords) {
        if (normalizedInput.includes(keyword.toLowerCase())) {
            scores.page += 1;
        }
    }

    console.log('📊 关键词匹配得分:', scores);

    // 找出最高得分的类型
    const maxScore = Math.max(scores.all, scores.plugin, scores.page);

    if (maxScore > 0) {
        let bestType = 'plugin'; // 默认为插件清空

        if (scores.all === maxScore) {
            bestType = 'all';
        } else if (scores.page === maxScore && scores.page > scores.plugin) {
            bestType = 'page';
        }

        const confidence = Math.min(maxScore / 2, 1.0); // 关键词匹配的置信度较低

        console.log('✅ 关键词匹配成功:', bestType, '置信度:', confidence);
        return { type: bestType, pattern: inputCommand, confidence, method: 'keyword' };
    }

    // 5. 模糊匹配（仅包含"清空"等基础词汇）
    if (hasClearKeyword && normalizedInput.length <= 10) {
        console.log('✅ 模糊匹配，默认为插件清空');
        return { type: 'plugin', pattern: inputCommand, confidence: 0.6, method: 'fuzzy' };
    }

    console.log('❌ 未匹配到清空指令');
    return null;
}

/**
 * 执行清空指令
 * @param {Object} clearInfo - 清空指令信息
 */
async function executeClearCommand(clearInfo) {
    console.log('🧹 执行清空指令:', clearInfo);

    try {
        switch (clearInfo.type) {
            case 'plugin':
                await clearPluginInput();
                break;
            case 'page':
                await clearPageInputs();
                break;
            case 'all':
                await clearAllInputs();
                break;
            default:
                throw new Error(`未知的清空类型: ${clearInfo.type}`);
        }

        console.log('✅ 清空指令执行成功');

    } catch (error) {
        console.error('❌ 清空指令执行失败:', error);
        updateStatus(`清空失败: ${error.message}`, 'error');
        throw error;
    }
}

/**
 * 清空插件输入框
 */
async function clearPluginInput() {
    console.log('🧹 清空插件输入框...');

    // 停止当前语音识别
    if (speechRecognition && isVoiceInputActive) {
        try {
            speechRecognition.stop();
        } catch (error) {
            console.warn('⚠️ 停止语音识别失败:', error);
        }
    }

    // 清空输入框
    if (commandInput) {
        commandInput.value = '';

        // 添加视觉反馈
        commandInput.style.borderColor = '#28a745';
        commandInput.style.boxShadow = '0 0 5px rgba(40, 167, 69, 0.5)';

        setTimeout(() => {
            commandInput.style.borderColor = '';
            commandInput.style.boxShadow = '';
        }, 1000);

        // 聚焦到输入框
        commandInput.focus();
    }

    // 重置状态变量
    lastCommand = '';
    lastInputLength = 0;
    isVoiceInputActive = false;

    // 更新语音指示器
    updateVoiceIndicator(false);

    // 清除定时器
    if (sendTimeout) {
        clearTimeout(sendTimeout);
        sendTimeout = null;
    }

    if (voiceInputTimeout) {
        clearTimeout(voiceInputTimeout);
        voiceInputTimeout = null;
    }

    // 显示成功提示
    updateStatus('插件输入框已清空', 'success');

    console.log('✅ 插件输入框清空完成');
}

/**
 * 清空页面输入框
 */
async function clearPageInputs() {
    console.log('🧹 清空页面输入框...');

    try {
        // 发送清空指令到content script
        await sendCommandToContent('', false, 'clear_page_inputs');

        // 显示成功提示
        updateStatus('页面输入框已清空', 'success');

        console.log('✅ 页面输入框清空完成');

    } catch (error) {
        console.error('❌ 清空页面输入框失败:', error);
        updateStatus('清空页面输入框失败，请确保页面支持此操作', 'error');
        throw error;
    }
}

/**
 * 清空所有输入框
 */
async function clearAllInputs() {
    console.log('🧹 清空所有输入框...');

    // 同时执行插件和页面清空
    await Promise.all([
        clearPluginInput(),
        clearPageInputs().catch(error => {
            console.warn('⚠️ 页面清空失败，但继续执行:', error);
        })
    ]);

    // 显示成功提示
    updateStatus('所有输入框已清空', 'success');

    console.log('✅ 所有输入框清空完成');
}

/**
 * 智能匹配语音快捷指令
 */
function matchShortcutCommand(inputCommand) {
    console.log('🔍 开始匹配快捷指令:', inputCommand);

    const normalizedInput = inputCommand.trim().toLowerCase();

    // 1. 精确匹配
    for (const [command, config] of shortcutCommands) {
        if (command.toLowerCase() === normalizedInput) {
            console.log('✅ 精确匹配成功:', command);
            return { command, config, matchType: 'exact' };
        }
    }

    // 2. 包含匹配
    for (const [command, config] of shortcutCommands) {
        if (normalizedInput.includes(command.toLowerCase()) || command.toLowerCase().includes(normalizedInput)) {
            console.log('✅ 包含匹配成功:', command);
            return { command, config, matchType: 'contains' };
        }
    }

    // 3. 模糊匹配（关键词匹配）
    const inputKeywords = normalizedInput.split(/\s+/);
    let bestMatch = null;
    let bestScore = 0;

    for (const [command, config] of shortcutCommands) {
        const commandKeywords = command.toLowerCase().split(/\s+/);
        let score = 0;

        for (const inputKeyword of inputKeywords) {
            for (const commandKeyword of commandKeywords) {
                if (commandKeyword.includes(inputKeyword) || inputKeyword.includes(commandKeyword)) {
                    score++;
                }
            }
        }

        if (score > bestScore && score > 0) {
            bestScore = score;
            bestMatch = { command, config, matchType: 'fuzzy', score };
        }
    }

    if (bestMatch) {
        console.log('✅ 模糊匹配成功:', bestMatch.command, '得分:', bestMatch.score);
        return bestMatch;
    }

    console.log('❌ 未找到匹配的快捷指令');
    return null;
}

/**
 * 执行快捷指令
 */
async function executeShortcutCommand(match) {
    try {
        console.log('🚀 执行快捷指令:', match);

        const { command, config } = match;

        // 记录使用统计
        await recordCommandUsage(command);

        // 打开目标URL
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        if (config.url === 'about:blank') {
            updateStatus(`快捷指令"${command}"需要配置URL`, 'warning');
            return `快捷指令"${command}"需要配置URL`;
        }

        // 在当前标签页打开URL
        await chrome.tabs.update(tab.id, { url: config.url });

        const successMessage = `已执行快捷指令: ${command} → ${config.url}`;
        updateStatus(successMessage, 'success');

        console.log('✅ 快捷指令执行成功:', successMessage);
        return successMessage;

    } catch (error) {
        console.error('❌ 快捷指令执行失败:', error);
        const errorMessage = `快捷指令执行失败: ${error.message}`;
        updateStatus(errorMessage, 'error');
        throw new Error(errorMessage);
    }
}

/**
 * 记录指令使用统计
 */
async function recordCommandUsage(command) {
    try {
        const currentCount = commandUsageStats.get(command) || 0;
        commandUsageStats.set(command, currentCount + 1);

        // 异步保存统计数据
        saveUsageStats().catch(error => {
            console.error('❌ 保存使用统计失败:', error);
        });

        console.log('📊 已记录指令使用:', command, '使用次数:', currentCount + 1);

    } catch (error) {
        console.error('❌ 记录使用统计失败:', error);
    }
}

/**
 * 添加新的快捷指令
 */
async function addShortcutCommand(command, url, description = '') {
    try {
        if (!command || !url) {
            throw new Error('指令名称和URL不能为空');
        }

        const config = {
            url: url,
            description: description || `打开 ${url}`,
            isDefault: false,
            createdAt: Date.now()
        };

        shortcutCommands.set(command, config);
        await saveShortcutCommands();

        console.log('✅ 已添加快捷指令:', command, '→', url);
        return true;

    } catch (error) {
        console.error('❌ 添加快捷指令失败:', error);
        throw error;
    }
}

/**
 * 删除快捷指令
 */
async function removeShortcutCommand(command) {
    try {
        if (!shortcutCommands.has(command)) {
            throw new Error('指令不存在');
        }

        shortcutCommands.delete(command);
        commandUsageStats.delete(command);

        await saveShortcutCommands();
        await saveUsageStats();

        console.log('✅ 已删除快捷指令:', command);
        return true;

    } catch (error) {
        console.error('❌ 删除快捷指令失败:', error);
        throw error;
    }
}

/**
 * 设置快捷指令管理界面事件
 */
function setupShortcutManagerEvents() {
    console.log('🔧 设置快捷指令管理界面事件...');

    // 快捷指令管理按钮
    const shortcutManagerButton = document.getElementById('shortcutManagerButton');
    if (shortcutManagerButton) {
        shortcutManagerButton.addEventListener('click', toggleShortcutManager);
        console.log('✅ 快捷指令管理按钮事件已绑定');
    } else {
        console.error('❌ 未找到快捷指令管理按钮');
    }

    // 关闭快捷指令管理界面
    const closeShortcutManager = document.getElementById('closeShortcutManager');
    if (closeShortcutManager) {
        closeShortcutManager.addEventListener('click', hideShortcutManager);
        console.log('✅ 关闭按钮事件已绑定');
    } else {
        console.error('❌ 未找到关闭按钮');
    }

    // 添加快捷指令按钮
    const addShortcutButton = document.getElementById('addShortcutButton');
    if (addShortcutButton) {
        addShortcutButton.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🔧 点击添加快捷指令按钮');
            handleAddShortcut();
        });
        console.log('✅ 添加快捷指令按钮事件已绑定');
    } else {
        console.error('❌ 未找到添加快捷指令按钮');
    }

    // 导出快捷指令按钮
    const exportShortcutsButton = document.getElementById('exportShortcutsButton');
    if (exportShortcutsButton) {
        exportShortcutsButton.addEventListener('click', handleExportShortcuts);
        console.log('✅ 导出按钮事件已绑定');
    } else {
        console.error('❌ 未找到导出按钮');
    }

    // 导入快捷指令按钮
    const importShortcutsButton = document.getElementById('importShortcutsButton');
    const importShortcutsInput = document.getElementById('importShortcutsInput');

    if (importShortcutsButton && importShortcutsInput) {
        importShortcutsButton.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🔧 点击导入快捷指令按钮');
            importShortcutsInput.click();
        });

        importShortcutsInput.addEventListener('change', handleImportShortcuts);
        console.log('✅ 导入按钮事件已绑定');
    } else {
        console.error('❌ 未找到导入按钮或文件输入框');
    }

    // 新快捷指令输入框回车事件
    const newShortcutCommand = document.getElementById('newShortcutCommand');
    const newShortcutUrl = document.getElementById('newShortcutUrl');

    if (newShortcutCommand && newShortcutUrl) {
        [newShortcutCommand, newShortcutUrl].forEach(input => {
            input.addEventListener('keydown', (event) => {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    console.log('⌨️ 回车键触发添加快捷指令');
                    handleAddShortcut();
                }
            });
        });
        console.log('✅ 输入框回车事件已绑定');
    } else {
        console.error('❌ 未找到新快捷指令输入框');
    }

    console.log('✅ 快捷指令管理界面事件设置完成');
}

/**
 * 切换快捷指令管理界面显示状态
 */
function toggleShortcutManager() {
    if (isShortcutManagerVisible) {
        hideShortcutManager();
    } else {
        showShortcutManager();
    }
}

/**
 * 显示快捷指令管理界面
 */
async function showShortcutManager() {
    const shortcutManager = document.getElementById('shortcutManager');
    if (!shortcutManager) return;

    shortcutManager.classList.remove('hidden');
    isShortcutManagerVisible = true;

    // 刷新快捷指令列表
    await refreshShortcutList();

    console.log('📋 快捷指令管理界面已显示');
}

/**
 * 隐藏快捷指令管理界面
 */
function hideShortcutManager() {
    const shortcutManager = document.getElementById('shortcutManager');
    if (!shortcutManager) return;

    shortcutManager.classList.add('hidden');
    isShortcutManagerVisible = false;

    console.log('📋 快捷指令管理界面已隐藏');
}

/**
 * 刷新快捷指令列表显示
 */
async function refreshShortcutList() {
    const shortcutList = document.getElementById('shortcutList');
    if (!shortcutList) return;

    // 清空现有列表
    shortcutList.innerHTML = '';

    if (shortcutCommands.size === 0) {
        shortcutList.innerHTML = '<p class="no-shortcuts">暂无快捷指令，请添加新的快捷指令</p>';
        return;
    }

    // 按使用频率排序
    const sortedCommands = Array.from(shortcutCommands.entries()).sort((a, b) => {
        const usageA = commandUsageStats.get(a[0]) || 0;
        const usageB = commandUsageStats.get(b[0]) || 0;
        return usageB - usageA;
    });

    // 生成快捷指令列表HTML
    for (const [command, config] of sortedCommands) {
        const usageCount = commandUsageStats.get(command) || 0;
        const shortcutItem = createShortcutItemElement(command, config, usageCount);
        shortcutList.appendChild(shortcutItem);
    }

    console.log('📋 快捷指令列表已刷新，共', shortcutCommands.size, '条');
}

/**
 * 创建快捷指令列表项元素
 */
function createShortcutItemElement(command, config, usageCount) {
    const item = document.createElement('div');
    item.className = 'shortcut-item';

    const isDefault = config.isDefault ? ' (默认)' : '';
    const usageText = usageCount > 0 ? ` • 使用 ${usageCount} 次` : '';

    item.innerHTML = `
        <div class="shortcut-info">
            <div class="shortcut-command">"${command}"${isDefault}</div>
            <div class="shortcut-url">${config.url}</div>
            <div class="shortcut-stats">${config.description}${usageText}</div>
        </div>
        <div class="shortcut-actions">
            <button class="edit-shortcut-btn" data-command="${command}">编辑</button>
            <button class="delete-shortcut-btn" data-command="${command}">删除</button>
        </div>
    `;

    // 绑定编辑和删除事件
    const editBtn = item.querySelector('.edit-shortcut-btn');
    const deleteBtn = item.querySelector('.delete-shortcut-btn');

    if (editBtn) {
        editBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🔧 点击编辑按钮:', command);
            handleEditShortcut(command, config);
        });
    } else {
        console.error('❌ 未找到编辑按钮');
    }

    if (deleteBtn) {
        deleteBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🗑️ 点击删除按钮:', command);
            handleDeleteShortcut(command);
        });
    } else {
        console.error('❌ 未找到删除按钮');
    }

    return item;
}

/**
 * 处理添加新快捷指令
 */
async function handleAddShortcut() {
    console.log('🔧 开始处理添加新快捷指令');

    const commandInput = document.getElementById('newShortcutCommand');
    const urlInput = document.getElementById('newShortcutUrl');

    if (!commandInput || !urlInput) {
        console.error('❌ 无法找到输入框元素');
        updateStatus('界面元素加载失败，请重新打开插件', 'error');
        return;
    }

    const command = commandInput.value.trim();
    const url = urlInput.value.trim();

    console.log('📝 输入内容:', { command, url });

    if (!command || !url) {
        updateStatus('请输入完整的指令名称和URL', 'error');
        return;
    }

    // 检查指令是否已存在
    if (shortcutCommands.has(command)) {
        updateStatus(`指令"${command}"已存在，请使用不同的名称`, 'error');
        return;
    }

    // 简单的URL格式验证
    try {
        new URL(url);
    } catch (e) {
        updateStatus('请输入有效的URL格式（如：https://example.com）', 'error');
        return;
    }

    try {
        console.log('💾 开始保存快捷指令...');
        await addShortcutCommand(command, url);

        // 清空输入框
        commandInput.value = '';
        urlInput.value = '';

        // 刷新列表
        console.log('🔄 刷新快捷指令列表...');
        await refreshShortcutList();

        updateStatus(`快捷指令"${command}"添加成功`, 'success');
        console.log('✅ 快捷指令添加成功:', command, '→', url);

    } catch (error) {
        updateStatus(`添加失败: ${error.message}`, 'error');
        console.error('❌ 添加快捷指令失败:', error);
    }
}

/**
 * 处理编辑快捷指令
 */
async function handleEditShortcut(command, config) {
    console.log('🔧 开始编辑快捷指令:', command, config);

    const newUrl = prompt(`编辑快捷指令"${command}"的URL:`, config.url);

    if (newUrl === null) {
        console.log('👤 用户取消编辑操作');
        return; // 用户取消
    }

    if (!newUrl.trim()) {
        updateStatus('URL不能为空', 'error');
        return;
    }

    // URL格式验证
    try {
        new URL(newUrl.trim());
    } catch (e) {
        updateStatus('请输入有效的URL格式（如：https://example.com）', 'error');
        return;
    }

    try {
        console.log('💾 开始更新快捷指令...');

        // 更新快捷指令
        const updatedConfig = {
            ...config,
            url: newUrl.trim(),
            description: `打开 ${newUrl.trim()}`,
            updatedAt: Date.now()
        };

        shortcutCommands.set(command, updatedConfig);
        await saveShortcutCommands();

        // 刷新列表
        console.log('🔄 刷新快捷指令列表...');
        await refreshShortcutList();

        updateStatus(`快捷指令"${command}"更新成功`, 'success');
        console.log('✅ 快捷指令更新成功:', command, '→', newUrl.trim());

    } catch (error) {
        updateStatus(`更新失败: ${error.message}`, 'error');
        console.error('❌ 更新快捷指令失败:', error);
    }
}

/**
 * 处理删除快捷指令
 */
async function handleDeleteShortcut(command) {
    console.log('🔧 开始删除快捷指令:', command);

    if (!confirm(`确定要删除快捷指令"${command}"吗？`)) {
        console.log('👤 用户取消删除操作');
        return;
    }

    try {
        console.log('🗑️ 开始删除快捷指令...');
        await removeShortcutCommand(command);

        // 刷新列表
        console.log('🔄 刷新快捷指令列表...');
        await refreshShortcutList();

        updateStatus(`快捷指令"${command}"删除成功`, 'success');
        console.log('✅ 快捷指令删除成功:', command);

    } catch (error) {
        updateStatus(`删除失败: ${error.message}`, 'error');
        console.error('❌ 删除快捷指令失败:', error);
    }
}

/**
 * 处理导出快捷指令配置
 */
async function handleExportShortcuts() {
    try {
        const exportData = {
            shortcuts: Object.fromEntries(shortcutCommands),
            usageStats: Object.fromEntries(commandUsageStats),
            exportTime: new Date().toISOString(),
            version: '1.0.0'
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        // 创建下载链接
        const downloadUrl = URL.createObjectURL(dataBlob);
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = `voice-shortcuts-${new Date().toISOString().split('T')[0]}.json`;

        // 触发下载
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // 清理URL对象
        URL.revokeObjectURL(downloadUrl);

        updateStatus('快捷指令配置导出成功', 'success');
        console.log('✅ 快捷指令配置导出成功');

    } catch (error) {
        updateStatus(`导出失败: ${error.message}`, 'error');
        console.error('❌ 导出快捷指令配置失败:', error);
    }
}

/**
 * 处理导入快捷指令配置
 */
async function handleImportShortcuts(event) {
    console.log('🔧 开始处理导入快捷指令配置');

    const file = event.target.files[0];
    if (!file) {
        console.log('❌ 未选择文件');
        return;
    }

    console.log('📁 选择的文件:', file.name, '大小:', file.size, 'bytes');

    try {
        console.log('📖 开始读取文件内容...');
        const fileContent = await readFileAsText(file);
        console.log('📄 文件内容长度:', fileContent.length);

        console.log('🔍 开始解析JSON...');
        const importData = JSON.parse(fileContent);
        console.log('📊 解析的数据:', importData);

        // 验证导入数据格式
        if (!importData.shortcuts || typeof importData.shortcuts !== 'object') {
            throw new Error('无效的配置文件格式：缺少shortcuts字段或格式错误');
        }

        // 确认导入
        const shortcutCount = Object.keys(importData.shortcuts).length;
        console.log('📋 待导入的快捷指令数量:', shortcutCount);

        if (shortcutCount === 0) {
            updateStatus('配置文件中没有快捷指令数据', 'error');
            return;
        }

        if (!confirm(`确定要导入 ${shortcutCount} 条快捷指令吗？这将覆盖现有配置。`)) {
            console.log('👤 用户取消导入操作');
            return;
        }

        console.log('💾 开始导入快捷指令...');

        // 导入快捷指令
        shortcutCommands = new Map(Object.entries(importData.shortcuts));
        console.log('✅ 快捷指令导入完成，共', shortcutCommands.size, '条');

        // 导入使用统计（如果存在）
        if (importData.usageStats && typeof importData.usageStats === 'object') {
            commandUsageStats = new Map(Object.entries(importData.usageStats));
            console.log('✅ 使用统计导入完成，共', commandUsageStats.size, '条');
        } else {
            console.log('⚠️ 配置文件中没有使用统计数据');
        }

        // 保存到存储
        console.log('💾 保存到本地存储...');
        await saveShortcutCommands();
        await saveUsageStats();

        // 刷新列表
        console.log('🔄 刷新快捷指令列表...');
        await refreshShortcutList();

        updateStatus(`成功导入 ${shortcutCount} 条快捷指令`, 'success');
        console.log('✅ 快捷指令配置导入成功，共', shortcutCount, '条');

    } catch (error) {
        let errorMessage = '导入失败';

        if (error instanceof SyntaxError) {
            errorMessage = '配置文件格式错误，请确保是有效的JSON文件';
        } else if (error.message.includes('无效的配置文件格式')) {
            errorMessage = error.message;
        } else {
            errorMessage = `导入失败: ${error.message}`;
        }

        updateStatus(errorMessage, 'error');
        console.error('❌ 导入快捷指令配置失败:', error);
    } finally {
        // 清空文件输入
        event.target.value = '';
        console.log('🧹 已清空文件输入');
    }
}

/**
 * 读取文件内容为文本
 */
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = () => reject(new Error('文件读取失败'));
        reader.readAsText(file);
    });
}

// 导出函数供调试使用
window.popupDebug = {
    sendCommand: sendCommandToContent,
    checkConnection: checkConnection,
    updateStatus: updateStatus,
    // 语音输入调试接口
    startVoiceInput: () => speechRecognition?.start(),
    stopVoiceInput: () => speechRecognition?.stop(),
    getVoiceStatus: () => ({
        isVoiceInputActive,
        speechRecognitionSupported: !!speechRecognition,
        lastInputLength
    }),
    // 状态查询接口
    getStatus: () => ({
        isConnected,
        lastCommand,
        isVoiceInputActive,
        speechRecognitionAvailable: !!speechRecognition
    }),
    // 快捷指令调试接口
    getShortcuts: () => shortcutCommands,
    getUsageStats: () => commandUsageStats,
    addShortcut: addShortcutCommand,
    removeShortcut: removeShortcutCommand,
    refreshShortcutList: refreshShortcutList,
    showShortcutManager: showShortcutManager,
    hideShortcutManager: hideShortcutManager,
    // 语音清空功能调试接口
    detectClearCommand: detectClearCommand,
    executeClearCommand: executeClearCommand,
    clearPluginInput: clearPluginInput,
    clearPageInputs: clearPageInputs,
    clearAllInputs: clearAllInputs,
    // 测试功能
    testAddShortcut: () => handleAddShortcut(),
    testImport: (data) => {
        // 创建模拟文件输入事件
        const mockEvent = {
            target: {
                files: [new Blob([JSON.stringify(data)], { type: 'application/json' })],
                value: ''
            }
        };
        return handleImportShortcuts(mockEvent);
    },
    // 清空功能测试
    testClearCommand: (command) => {
        const clearMatch = detectClearCommand(command);
        if (clearMatch) {
            return executeClearCommand(clearMatch);
        } else {
            console.log('❌ 未检测到清空指令:', command);
            return null;
        }
    }
};
