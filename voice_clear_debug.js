/**
 * 语音清除功能调试和诊断工具
 * 用于诊断语音清除功能的问题并提供解决方案
 */

/**
 * 语音清除功能诊断器
 */
class VoiceClearDiagnostic {
    constructor() {
        this.issues = [];
        this.suggestions = [];
    }

    /**
     * 运行完整诊断
     */
    async runFullDiagnostic() {
        console.log('🔍 开始语音清除功能诊断...\n');

        this.issues = [];
        this.suggestions = [];

        // 1. 检查基础环境
        await this.checkBasicEnvironment();

        // 2. 检查权限配置
        await this.checkPermissions();

        // 3. 检查Content Script状态
        await this.checkContentScript();

        // 4. 检查消息传递机制
        await this.checkMessagePassing();

        // 5. 检查DOM操作能力
        await this.checkDOMOperations();

        // 6. 检查页面兼容性
        await this.checkPageCompatibility();

        // 输出诊断结果
        this.printDiagnosticResults();

        return {
            issues: this.issues,
            suggestions: this.suggestions
        };
    }

    /**
     * 检查基础环境
     */
    async checkBasicEnvironment() {
        console.log('🔧 检查基础环境...');

        // 检查浏览器类型
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Chrome')) {
            console.log('✅ 浏览器: Chrome');
        } else if (userAgent.includes('Edge')) {
            console.log('✅ 浏览器: Edge');
        } else {
            this.addIssue('浏览器兼容性', '当前浏览器可能不完全支持Chrome扩展API');
            this.addSuggestion('建议使用Chrome或Edge浏览器');
        }

        // 检查扩展API可用性
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            console.log('✅ Chrome扩展API可用');
        } else {
            this.addIssue('扩展API', 'Chrome扩展API不可用');
            this.addSuggestion('请确保在扩展环境中运行此脚本');
        }

        // 检查当前页面类型
        const url = window.location.href;
        if (url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
            this.addIssue('页面类型', '当前在浏览器内部页面，无法执行页面清空操作');
            this.addSuggestion('请在普通网页中测试页面清空功能');
        } else {
            console.log('✅ 当前页面支持扩展操作');
        }
    }

    /**
     * 检查权限配置
     */
    async checkPermissions() {
        console.log('🔐 检查权限配置...');

        try {
            // 检查activeTab权限
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs && tabs.length > 0) {
                console.log('✅ activeTab权限正常');
            } else {
                this.addIssue('权限配置', 'activeTab权限可能有问题');
                this.addSuggestion('检查manifest.json中的permissions配置');
            }

            // 检查scripting权限
            if (chrome.scripting) {
                console.log('✅ scripting权限正常');
            } else {
                this.addIssue('权限配置', 'scripting权限不可用');
                this.addSuggestion('确保manifest.json中包含"scripting"权限');
            }

        } catch (error) {
            this.addIssue('权限检查', `权限检查失败: ${error.message}`);
            this.addSuggestion('检查扩展权限配置和manifest.json文件');
        }
    }

    /**
     * 检查Content Script状态
     */
    async checkContentScript() {
        console.log('📜 检查Content Script状态...');

        try {
            // 检查content script是否已注入
            const response = await this.sendTestMessage('ping');
            
            if (response && response.success) {
                console.log('✅ Content Script已正确注入并响应');
                console.log('   响应信息:', response.message);
            } else {
                this.addIssue('Content Script', 'Content Script未正确响应');
                this.addSuggestion('尝试刷新页面或重新加载扩展');
            }

        } catch (error) {
            this.addIssue('Content Script', `Content Script连接失败: ${error.message}`);
            this.addSuggestion('检查content.js文件是否正确加载，或尝试手动注入');
        }
    }

    /**
     * 检查消息传递机制
     */
    async checkMessagePassing() {
        console.log('📨 检查消息传递机制...');

        try {
            // 测试基本消息传递
            const pingResponse = await this.sendTestMessage('ping');
            if (pingResponse && pingResponse.success) {
                console.log('✅ 基本消息传递正常');
            }

            // 测试清空指令消息
            const clearResponse = await this.sendTestMessage('clear_page_inputs');
            if (clearResponse && clearResponse.success) {
                console.log('✅ 清空指令消息传递正常');
            } else {
                this.addIssue('消息传递', '清空指令消息传递失败');
                this.addSuggestion('检查content.js中的handleClearPageInputs函数');
            }

        } catch (error) {
            this.addIssue('消息传递', `消息传递测试失败: ${error.message}`);
            this.addSuggestion('检查popup.js和content.js之间的消息传递逻辑');
        }
    }

    /**
     * 检查DOM操作能力
     */
    async checkDOMOperations() {
        console.log('🌐 检查DOM操作能力...');

        // 创建测试输入框
        const testInput = document.createElement('input');
        testInput.type = 'text';
        testInput.value = '测试内容';
        testInput.id = 'voice-clear-diagnostic-test';
        testInput.style.display = 'none';
        document.body.appendChild(testInput);

        try {
            // 测试基本DOM查询
            const foundInput = document.getElementById('voice-clear-diagnostic-test');
            if (foundInput) {
                console.log('✅ DOM查询功能正常');
            } else {
                this.addIssue('DOM操作', 'DOM查询功能异常');
            }

            // 测试值清空
            foundInput.value = '';
            if (foundInput.value === '') {
                console.log('✅ DOM值清空功能正常');
            } else {
                this.addIssue('DOM操作', 'DOM值清空功能异常');
            }

            // 测试事件触发
            const inputEvent = new Event('input', { bubbles: true });
            foundInput.dispatchEvent(inputEvent);
            console.log('✅ DOM事件触发功能正常');

        } catch (error) {
            this.addIssue('DOM操作', `DOM操作测试失败: ${error.message}`);
            this.addSuggestion('检查页面的Content Security Policy设置');
        } finally {
            // 清理测试元素
            const testElement = document.getElementById('voice-clear-diagnostic-test');
            if (testElement) {
                testElement.remove();
            }
        }
    }

    /**
     * 检查页面兼容性
     */
    async checkPageCompatibility() {
        console.log('🔍 检查页面兼容性...');

        // 检查页面上的输入框数量
        const inputSelectors = [
            'input[type="text"]',
            'input[type="password"]',
            'input[type="email"]',
            'textarea',
            '[contenteditable="true"]'
        ];

        let totalInputs = 0;
        inputSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            totalInputs += elements.length;
            console.log(`   ${selector}: ${elements.length}个`);
        });

        if (totalInputs > 0) {
            console.log(`✅ 页面包含${totalInputs}个可清空的输入框`);
        } else {
            this.addIssue('页面兼容性', '页面上没有找到可清空的输入框');
            this.addSuggestion('在包含输入框的页面中测试清空功能');
        }

        // 检查页面是否有特殊限制
        try {
            const csp = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
            if (csp) {
                console.log('⚠️ 页面设置了Content Security Policy，可能影响扩展功能');
                this.addSuggestion('如果功能异常，可能与页面的CSP设置有关');
            }
        } catch (error) {
            // 忽略CSP检查错误
        }
    }

    /**
     * 发送测试消息
     */
    async sendTestMessage(action) {
        return new Promise((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (!tabs || tabs.length === 0) {
                    reject(new Error('无法获取当前标签页'));
                    return;
                }

                const message = {
                    type: 'COMMAND_FROM_POPUP',
                    command: '',
                    action: action,
                    timestamp: Date.now()
                };

                chrome.tabs.sendMessage(tabs[0].id, message, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
        });
    }

    /**
     * 添加问题
     */
    addIssue(category, description) {
        this.issues.push({ category, description });
        console.log(`❌ ${category}: ${description}`);
    }

    /**
     * 添加建议
     */
    addSuggestion(suggestion) {
        this.suggestions.push(suggestion);
        console.log(`💡 建议: ${suggestion}`);
    }

    /**
     * 输出诊断结果
     */
    printDiagnosticResults() {
        console.log('\n📊 诊断结果总结:');
        
        if (this.issues.length === 0) {
            console.log('✅ 未发现问题，语音清除功能应该正常工作');
        } else {
            console.log(`❌ 发现 ${this.issues.length} 个问题:`);
            this.issues.forEach((issue, index) => {
                console.log(`   ${index + 1}. [${issue.category}] ${issue.description}`);
            });
        }

        if (this.suggestions.length > 0) {
            console.log('\n💡 建议解决方案:');
            this.suggestions.forEach((suggestion, index) => {
                console.log(`   ${index + 1}. ${suggestion}`);
            });
        }

        console.log('\n🔧 手动测试步骤:');
        console.log('   1. 在页面输入框中输入一些内容');
        console.log('   2. 打开扩展popup');
        console.log('   3. 使用语音或键盘输入"清空页面输入框"');
        console.log('   4. 观察页面输入框是否被清空');
        console.log('   5. 检查浏览器控制台的日志信息');
    }

    /**
     * 生成修复脚本
     */
    generateFixScript() {
        console.log('\n🛠️ 生成修复脚本...');
        
        let fixScript = `
// 语音清除功能修复脚本
// 请在浏览器控制台中执行以下代码

console.log('🔧 开始修复语音清除功能...');

// 1. 重新注入Content Script
async function reinjectContentScript() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
        });
        console.log('✅ Content Script重新注入成功');
    } catch (error) {
        console.error('❌ Content Script注入失败:', error);
    }
}

// 2. 测试页面清空功能
async function testPageClear() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const response = await chrome.tabs.sendMessage(tab.id, {
            type: 'COMMAND_FROM_POPUP',
            action: 'clear_page_inputs',
            timestamp: Date.now()
        });
        console.log('✅ 页面清空测试结果:', response);
    } catch (error) {
        console.error('❌ 页面清空测试失败:', error);
    }
}

// 执行修复
reinjectContentScript().then(() => {
    setTimeout(testPageClear, 1000);
});
        `;

        console.log(fixScript);
        return fixScript;
    }
}

// 导出诊断器
window.VoiceClearDiagnostic = VoiceClearDiagnostic;

// 提供快速诊断函数
window.diagnoseClearFunction = async function() {
    const diagnostic = new VoiceClearDiagnostic();
    return await diagnostic.runFullDiagnostic();
};

// 提供快速修复函数
window.fixClearFunction = function() {
    const diagnostic = new VoiceClearDiagnostic();
    return diagnostic.generateFixScript();
};

console.log('🔍 语音清除功能诊断工具已加载');
console.log('💡 使用方法:');
console.log('   - 运行完整诊断: diagnoseClearFunction()');
console.log('   - 生成修复脚本: fixClearFunction()');
