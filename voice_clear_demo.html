<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 语音清空功能演示页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #4285f4;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .demo-section h2 {
            color: #333;
            margin-top: 0;
            font-size: 20px;
            border-bottom: 2px solid #4285f4;
            padding-bottom: 10px;
        }

        .input-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .input-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .input-group input,
        .input-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }

        .input-group input:focus,
        .input-group textarea:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 5px rgba(66, 133, 244, 0.3);
        }

        .input-group textarea {
            height: 80px;
            resize: vertical;
        }

        .command-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .command-list li {
            background: white;
            margin: 8px 0;
            padding: 12px 16px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .command-list li:hover {
            background: #f0f7ff;
        }

        .command-list li.plugin {
            border-left-color: #4285f4;
        }

        .command-list li.page {
            border-left-color: #ff9800;
        }

        .command-list li.all {
            border-left-color: #e91e63;
        }

        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }

        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }

        .status-display {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
        }

        .btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #3367d6;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #333;
        }

        .btn-danger {
            background: #dc3545;
        }

        .legend {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 4px;
            border-radius: 2px;
        }

        .contenteditable-demo {
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 4px;
            min-height: 60px;
            background: white;
        }

        .contenteditable-demo:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 5px rgba(66, 133, 244, 0.3);
        }

        .test-results {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 语音清空功能演示页面</h1>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <p>1. 在下方的输入框中输入一些内容</p>
            <p>2. 点击插件图标打开popup界面</p>
            <p>3. 使用语音输入或键盘输入清空指令</p>
            <p>4. 观察不同类型的清空效果</p>
            <p>5. 查看浏览器控制台获取详细日志</p>
        </div>

        <div class="demo-section">
            <h2>🎯 清空指令类型说明</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #4285f4;"></div>
                    <span>插件清空指令</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff9800;"></div>
                    <span>页面清空指令</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e91e63;"></div>
                    <span>全部清空指令</span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔵 插件输入框清空指令</h2>
            <p>这些指令只会清空插件popup中的输入框：</p>
            <ul class="command-list">
                <li class="plugin" onclick="copyToClipboard(this)">清空输入框</li>
                <li class="plugin" onclick="copyToClipboard(this)">清除指令</li>
                <li class="plugin" onclick="copyToClipboard(this)">重置输入</li>
                <li class="plugin" onclick="copyToClipboard(this)">清空命令框</li>
                <li class="plugin" onclick="copyToClipboard(this)">清空插件</li>
                <li class="plugin" onclick="copyToClipboard(this)">clear input</li>
                <li class="plugin" onclick="copyToClipboard(this)">reset command</li>
                <li class="plugin" onclick="copyToClipboard(this)">clear plugin</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🟠 页面输入框清空指令</h2>
            <p>这些指令只会清空当前页面上的输入框：</p>
            <ul class="command-list">
                <li class="page" onclick="copyToClipboard(this)">清空页面输入框</li>
                <li class="page" onclick="copyToClipboard(this)">清除网页内容</li>
                <li class="page" onclick="copyToClipboard(this)">删除表单内容</li>
                <li class="page" onclick="copyToClipboard(this)">清空网页</li>
                <li class="page" onclick="copyToClipboard(this)">清空表单</li>
                <li class="page" onclick="copyToClipboard(this)">clear page input</li>
                <li class="page" onclick="copyToClipboard(this)">clear form</li>
                <li class="page" onclick="copyToClipboard(this)">clear website</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🔴 全部清空指令</h2>
            <p>这些指令会同时清空插件和页面的输入框：</p>
            <ul class="command-list">
                <li class="all" onclick="copyToClipboard(this)">全部清空</li>
                <li class="all" onclick="copyToClipboard(this)">清空所有</li>
                <li class="all" onclick="copyToClipboard(this)">重置全部</li>
                <li class="all" onclick="copyToClipboard(this)">清空一切</li>
                <li class="all" onclick="copyToClipboard(this)">clear all</li>
                <li class="all" onclick="copyToClipboard(this)">reset everything</li>
                <li class="all" onclick="copyToClipboard(this)">delete all</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>📝 测试输入框区域</h2>
            <p>在这些输入框中输入内容，然后测试清空功能：</p>

            <div class="input-demo">
                <div class="input-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" placeholder="请输入用户名">
                </div>

                <div class="input-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" placeholder="请输入密码">
                </div>

                <div class="input-group">
                    <label for="email">邮箱</label>
                    <input type="email" id="email" placeholder="请输入邮箱地址">
                </div>

                <div class="input-group">
                    <label for="phone">电话</label>
                    <input type="tel" id="phone" placeholder="请输入电话号码">
                </div>

                <div class="input-group">
                    <label for="website">网站</label>
                    <input type="url" id="website" placeholder="请输入网站地址">
                </div>

                <div class="input-group">
                    <label for="search">搜索</label>
                    <input type="search" id="search" placeholder="请输入搜索关键词">
                </div>
            </div>

            <div class="input-demo">
                <div class="input-group">
                    <label for="comments">评论</label>
                    <textarea id="comments" placeholder="请输入您的评论..."></textarea>
                </div>

                <div class="input-group">
                    <label for="description">描述</label>
                    <textarea id="description" placeholder="请输入详细描述..."></textarea>
                </div>
            </div>

            <div class="input-demo">
                <div class="input-group">
                    <label for="editable">可编辑内容</label>
                    <div id="editable" class="contenteditable-demo" contenteditable="true">
                        点击这里输入可编辑内容...
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🧪 快速测试</h2>
            <button class="btn" onclick="fillAllInputs()">填充所有输入框</button>
            <button class="btn btn-warning" onclick="clearAllInputsManually()">手动清空所有</button>
            <button class="btn btn-success" onclick="showTestResults()">显示测试结果</button>

            <div id="test-results" class="test-results" style="display: none;">
                <h4>测试结果：</h4>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔧 调试工具</h2>
            <p>如果语音清空功能不工作，请使用以下调试工具：</p>
            <button class="btn" onclick="runDiagnostic()">运行功能诊断</button>
            <button class="btn btn-warning" onclick="runFunctionTest()">运行功能测试</button>
            <button class="btn btn-danger" onclick="generateFixScript()">生成修复脚本</button>

            <div id="diagnostic-results" class="test-results" style="display: none;">
                <h4>诊断结果：</h4>
                <div id="diagnostic-content"></div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📊 操作日志</h2>
            <div id="operation-log" class="status-display">
                等待操作...
            </div>
        </div>
    </div>

    <script>
        // 复制指令到剪贴板
        function copyToClipboard(element) {
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                logMessage(`已复制指令: "${text}"`, 'info');
                element.style.background = '#d4edda';
                setTimeout(() => {
                    element.style.background = '';
                }, 1000);
            }).catch(err => {
                logMessage(`复制失败: ${err.message}`, 'error');
            });
        }

        // 填充所有输入框
        function fillAllInputs() {
            const inputs = document.querySelectorAll('input, textarea');
            const editableElements = document.querySelectorAll('[contenteditable="true"]');

            inputs.forEach((input, index) => {
                if (input.type === 'password') {
                    input.value = '123456';
                } else {
                    input.value = `测试内容 ${index + 1}`;
                }
            });

            editableElements.forEach((element, index) => {
                element.textContent = `可编辑测试内容 ${index + 1}`;
            });

            logMessage('已填充所有输入框', 'success');
        }

        // 手动清空所有输入框
        function clearAllInputsManually() {
            const inputs = document.querySelectorAll('input, textarea');
            const editableElements = document.querySelectorAll('[contenteditable="true"]');

            inputs.forEach(input => {
                input.value = '';
            });

            editableElements.forEach(element => {
                element.textContent = '';
            });

            logMessage('已手动清空所有输入框', 'warning');
        }

        // 显示测试结果
        function showTestResults() {
            const resultsDiv = document.getElementById('test-results');
            const contentDiv = document.getElementById('results-content');

            const inputs = document.querySelectorAll('input, textarea');
            const editableElements = document.querySelectorAll('[contenteditable="true"]');

            let filledCount = 0;
            let emptyCount = 0;

            inputs.forEach(input => {
                if (input.value.trim()) {
                    filledCount++;
                } else {
                    emptyCount++;
                }
            });

            editableElements.forEach(element => {
                if (element.textContent.trim()) {
                    filledCount++;
                } else {
                    emptyCount++;
                }
            });

            contentDiv.innerHTML = `
                <p class="info">总输入框数量: ${inputs.length + editableElements.length}</p>
                <p class="success">有内容的输入框: ${filledCount}</p>
                <p class="warning">空的输入框: ${emptyCount}</p>
                <p><small>时间: ${new Date().toLocaleTimeString()}</small></p>
            `;

            resultsDiv.style.display = 'block';
            logMessage(`测试结果 - 有内容: ${filledCount}, 空的: ${emptyCount}`, 'info');
        }

        // 记录日志
        function logMessage(message, type = 'info') {
            const logDiv = document.getElementById('operation-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;

            const logEntry = document.createElement('div');
            logEntry.className = className;
            logEntry.innerHTML = `[${timestamp}] ${message}`;

            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            logMessage('语音清空功能演示页面已加载', 'success');
            logMessage('提示：点击上方的指令可以复制到剪贴板', 'info');

            // 监听输入框变化
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    logMessage(`输入框 "${input.placeholder || input.id}" 内容已更改`, 'info');
                });
            });
        });

        // 监听页面消息（如果插件发送的话）
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'CLEAR_EXECUTED') {
                logMessage(`清空操作执行: ${event.data.message}`, 'success');
            }
        });

        // 调试工具函数
        async function runDiagnostic() {
            logMessage('开始运行功能诊断...', 'info');

            try {
                // 动态加载诊断脚本
                if (!window.VoiceClearDiagnostic) {
                    await loadScript('voice_clear_debug.js');
                }

                const diagnostic = new window.VoiceClearDiagnostic();
                const results = await diagnostic.runFullDiagnostic();

                // 显示诊断结果
                const resultsDiv = document.getElementById('diagnostic-results');
                const contentDiv = document.getElementById('diagnostic-content');

                let resultHtml = '<h5>诊断完成</h5>';

                if (results.issues.length === 0) {
                    resultHtml += '<p class="success">✅ 未发现问题，功能应该正常工作</p>';
                } else {
                    resultHtml += `<p class="error">❌ 发现 ${results.issues.length} 个问题:</p><ul>`;
                    results.issues.forEach(issue => {
                        resultHtml += `<li class="error">[${issue.category}] ${issue.description}</li>`;
                    });
                    resultHtml += '</ul>';
                }

                if (results.suggestions.length > 0) {
                    resultHtml += '<p class="info">💡 建议解决方案:</p><ul>';
                    results.suggestions.forEach(suggestion => {
                        resultHtml += `<li class="info">${suggestion}</li>`;
                    });
                    resultHtml += '</ul>';
                }

                contentDiv.innerHTML = resultHtml;
                resultsDiv.style.display = 'block';

                logMessage('功能诊断完成', 'success');

            } catch (error) {
                logMessage(`诊断失败: ${error.message}`, 'error');
            }
        }

        async function runFunctionTest() {
            logMessage('开始运行功能测试...', 'info');

            try {
                // 动态加载测试脚本
                if (!window.VoiceClearTestSuite) {
                    await loadScript('voice_clear_test.js');
                }

                const testSuite = new window.VoiceClearTestSuite();
                const results = await testSuite.runAllTests();

                logMessage(`功能测试完成 - 通过率: ${results.passRate}%`,
                    results.failed === 0 ? 'success' : 'warning');

            } catch (error) {
                logMessage(`测试失败: ${error.message}`, 'error');
            }
        }

        async function generateFixScript() {
            logMessage('生成修复脚本...', 'info');

            try {
                // 动态加载诊断脚本
                if (!window.VoiceClearDiagnostic) {
                    await loadScript('voice_clear_debug.js');
                }

                const diagnostic = new window.VoiceClearDiagnostic();
                const fixScript = diagnostic.generateFixScript();

                // 复制到剪贴板
                await navigator.clipboard.writeText(fixScript);
                logMessage('修复脚本已复制到剪贴板，请在浏览器控制台中粘贴执行', 'success');

            } catch (error) {
                logMessage(`生成修复脚本失败: ${error.message}`, 'error');
            }
        }

        // 动态加载脚本的辅助函数
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
    </script>
</body>
</html>
