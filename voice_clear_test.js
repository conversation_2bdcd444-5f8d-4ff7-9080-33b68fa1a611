/**
 * 语音清除功能测试脚本
 * 用于验证语音清除功能的各个组件是否正常工作
 */

// 测试配置
const TEST_CONFIG = {
    // 测试超时时间（毫秒）
    timeout: 5000,
    // 是否显示详细日志
    verbose: true,
    // 测试重试次数
    retryCount: 3
};

/**
 * 测试结果收集器
 */
class TestResultCollector {
    constructor() {
        this.results = [];
        this.startTime = Date.now();
    }

    addResult(testName, success, message, details = null) {
        this.results.push({
            testName,
            success,
            message,
            details,
            timestamp: Date.now()
        });

        const status = success ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
        
        if (details && TEST_CONFIG.verbose) {
            console.log('   详细信息:', details);
        }
    }

    getSummary() {
        const total = this.results.length;
        const passed = this.results.filter(r => r.success).length;
        const failed = total - passed;
        const duration = Date.now() - this.startTime;

        return {
            total,
            passed,
            failed,
            duration,
            passRate: total > 0 ? (passed / total * 100).toFixed(1) : 0
        };
    }

    printSummary() {
        const summary = this.getSummary();
        console.log('\n📊 测试总结:');
        console.log(`   总测试数: ${summary.total}`);
        console.log(`   通过: ${summary.passed}`);
        console.log(`   失败: ${summary.failed}`);
        console.log(`   通过率: ${summary.passRate}%`);
        console.log(`   耗时: ${summary.duration}ms`);
        
        if (summary.failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.results.filter(r => !r.success).forEach(result => {
                console.log(`   - ${result.testName}: ${result.message}`);
            });
        }
    }
}

/**
 * 语音清除功能测试套件
 */
class VoiceClearTestSuite {
    constructor() {
        this.collector = new TestResultCollector();
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 开始语音清除功能测试...\n');

        try {
            // 1. 基础功能测试
            await this.testBasicFunctions();

            // 2. 指令检测测试
            await this.testCommandDetection();

            // 3. 页面清空功能测试
            await this.testPageClearFunction();

            // 4. 错误处理测试
            await this.testErrorHandling();

            // 5. 集成测试
            await this.testIntegration();

        } catch (error) {
            this.collector.addResult('测试套件执行', false, `测试执行出错: ${error.message}`, error);
        }

        this.collector.printSummary();
        return this.collector.getSummary();
    }

    /**
     * 测试基础功能
     */
    async testBasicFunctions() {
        console.log('🔧 测试基础功能...');

        // 测试清空指令模式是否正确定义
        try {
            if (typeof window.popupDebug !== 'undefined' && window.popupDebug.clearCommandPatterns) {
                const patterns = window.popupDebug.clearCommandPatterns;
                
                const hasPluginPatterns = patterns.plugin && patterns.plugin.chinese && patterns.plugin.chinese.length > 0;
                const hasPagePatterns = patterns.page && patterns.page.chinese && patterns.page.chinese.length > 0;
                const hasAllPatterns = patterns.all && patterns.all.chinese && patterns.all.chinese.length > 0;

                if (hasPluginPatterns && hasPagePatterns && hasAllPatterns) {
                    this.collector.addResult('清空指令模式定义', true, '所有清空指令模式已正确定义');
                } else {
                    this.collector.addResult('清空指令模式定义', false, '清空指令模式定义不完整');
                }
            } else {
                this.collector.addResult('清空指令模式定义', false, '无法访问清空指令模式');
            }
        } catch (error) {
            this.collector.addResult('清空指令模式定义', false, `测试出错: ${error.message}`);
        }

        // 测试检测函数是否存在
        try {
            if (typeof window.popupDebug !== 'undefined' && typeof window.popupDebug.detectClearCommand === 'function') {
                this.collector.addResult('清空指令检测函数', true, '清空指令检测函数存在且可调用');
            } else {
                this.collector.addResult('清空指令检测函数', false, '清空指令检测函数不存在或不可调用');
            }
        } catch (error) {
            this.collector.addResult('清空指令检测函数', false, `测试出错: ${error.message}`);
        }
    }

    /**
     * 测试指令检测功能
     */
    async testCommandDetection() {
        console.log('🔍 测试指令检测功能...');

        const testCases = [
            // 插件清空指令测试
            { command: '清空输入框', expectedType: 'plugin', description: '插件清空指令' },
            { command: '清除指令', expectedType: 'plugin', description: '插件清空指令' },
            { command: 'clear input', expectedType: 'plugin', description: '英文插件清空指令' },
            
            // 页面清空指令测试
            { command: '清空页面输入框', expectedType: 'page', description: '页面清空指令' },
            { command: '清空表单', expectedType: 'page', description: '页面清空指令' },
            { command: 'clear form', expectedType: 'page', description: '英文页面清空指令' },
            
            // 全部清空指令测试
            { command: '全部清空', expectedType: 'all', description: '全部清空指令' },
            { command: '清空所有', expectedType: 'all', description: '全部清空指令' },
            { command: 'clear all', expectedType: 'all', description: '英文全部清空指令' },
            
            // 非清空指令测试
            { command: '打开网页', expectedType: null, description: '非清空指令' },
            { command: '点击按钮', expectedType: null, description: '非清空指令' }
        ];

        for (const testCase of testCases) {
            try {
                if (typeof window.popupDebug !== 'undefined' && typeof window.popupDebug.detectClearCommand === 'function') {
                    const result = window.popupDebug.detectClearCommand(testCase.command);
                    
                    if (testCase.expectedType === null) {
                        // 期望不匹配
                        if (result === null) {
                            this.collector.addResult(`指令检测: ${testCase.command}`, true, `正确识别为非清空指令`);
                        } else {
                            this.collector.addResult(`指令检测: ${testCase.command}`, false, `错误识别为清空指令: ${result.type}`);
                        }
                    } else {
                        // 期望匹配
                        if (result && result.type === testCase.expectedType) {
                            this.collector.addResult(`指令检测: ${testCase.command}`, true, `正确识别为${testCase.expectedType}类型`);
                        } else {
                            this.collector.addResult(`指令检测: ${testCase.command}`, false, `识别错误，期望${testCase.expectedType}，实际${result?.type || 'null'}`);
                        }
                    }
                } else {
                    this.collector.addResult(`指令检测: ${testCase.command}`, false, '检测函数不可用');
                }
            } catch (error) {
                this.collector.addResult(`指令检测: ${testCase.command}`, false, `测试出错: ${error.message}`);
            }
        }
    }

    /**
     * 测试页面清空功能
     */
    async testPageClearFunction() {
        console.log('🧹 测试页面清空功能...');

        // 创建测试输入框
        const testInputs = this.createTestInputs();

        try {
            // 填充测试数据
            testInputs.forEach((input, index) => {
                if (input.tagName.toLowerCase() === 'input') {
                    input.value = `测试内容${index + 1}`;
                } else if (input.tagName.toLowerCase() === 'textarea') {
                    input.value = `测试文本${index + 1}`;
                } else if (input.hasAttribute('contenteditable')) {
                    input.textContent = `可编辑内容${index + 1}`;
                }
            });

            this.collector.addResult('测试输入框创建', true, `成功创建${testInputs.length}个测试输入框`);

            // 测试页面清空功能
            if (typeof window.popupDebug !== 'undefined' && typeof window.popupDebug.clearPageInputs === 'function') {
                await window.popupDebug.clearPageInputs();

                // 检查是否清空成功
                let clearedCount = 0;
                testInputs.forEach(input => {
                    const value = this.getInputValue(input);
                    if (!value || value.trim() === '') {
                        clearedCount++;
                    }
                });

                if (clearedCount === testInputs.length) {
                    this.collector.addResult('页面清空功能', true, `成功清空${clearedCount}个输入框`);
                } else {
                    this.collector.addResult('页面清空功能', false, `只清空了${clearedCount}/${testInputs.length}个输入框`);
                }
            } else {
                this.collector.addResult('页面清空功能', false, '页面清空函数不可用');
            }

        } catch (error) {
            this.collector.addResult('页面清空功能', false, `测试出错: ${error.message}`);
        } finally {
            // 清理测试输入框
            this.cleanupTestInputs(testInputs);
        }
    }

    /**
     * 测试错误处理
     */
    async testErrorHandling() {
        console.log('⚠️ 测试错误处理...');

        // 测试无效指令处理
        try {
            if (typeof window.popupDebug !== 'undefined' && typeof window.popupDebug.detectClearCommand === 'function') {
                const result = window.popupDebug.detectClearCommand('');
                if (result === null) {
                    this.collector.addResult('空指令处理', true, '正确处理空指令');
                } else {
                    this.collector.addResult('空指令处理', false, '空指令处理异常');
                }
            }
        } catch (error) {
            this.collector.addResult('空指令处理', false, `测试出错: ${error.message}`);
        }

        // 测试特殊字符处理
        try {
            if (typeof window.popupDebug !== 'undefined' && typeof window.popupDebug.detectClearCommand === 'function') {
                const result = window.popupDebug.detectClearCommand('!@#$%^&*()');
                if (result === null) {
                    this.collector.addResult('特殊字符处理', true, '正确处理特殊字符');
                } else {
                    this.collector.addResult('特殊字符处理', false, '特殊字符处理异常');
                }
            }
        } catch (error) {
            this.collector.addResult('特殊字符处理', false, `测试出错: ${error.message}`);
        }
    }

    /**
     * 测试集成功能
     */
    async testIntegration() {
        console.log('🔗 测试集成功能...');

        // 测试完整的清空流程
        try {
            if (typeof window.popupDebug !== 'undefined' && typeof window.popupDebug.testClearCommand === 'function') {
                const result = await window.popupDebug.testClearCommand('清空输入框');
                if (result) {
                    this.collector.addResult('集成测试', true, '完整清空流程测试通过');
                } else {
                    this.collector.addResult('集成测试', false, '完整清空流程测试失败');
                }
            } else {
                this.collector.addResult('集成测试', false, '集成测试函数不可用');
            }
        } catch (error) {
            this.collector.addResult('集成测试', false, `测试出错: ${error.message}`);
        }
    }

    /**
     * 创建测试输入框
     */
    createTestInputs() {
        const inputs = [];
        const container = document.createElement('div');
        container.id = 'voice-clear-test-container';
        container.style.display = 'none';

        // 创建不同类型的输入框
        const inputTypes = [
            { tag: 'input', type: 'text' },
            { tag: 'input', type: 'password' },
            { tag: 'input', type: 'email' },
            { tag: 'textarea' },
            { tag: 'div', contenteditable: true }
        ];

        inputTypes.forEach((config, index) => {
            const element = document.createElement(config.tag);
            element.id = `test-input-${index}`;
            
            if (config.type) {
                element.type = config.type;
            }
            
            if (config.contenteditable) {
                element.contentEditable = true;
            }

            container.appendChild(element);
            inputs.push(element);
        });

        document.body.appendChild(container);
        return inputs;
    }

    /**
     * 获取输入框的值
     */
    getInputValue(input) {
        if (input.tagName.toLowerCase() === 'input' || input.tagName.toLowerCase() === 'textarea') {
            return input.value;
        } else if (input.hasAttribute('contenteditable')) {
            return input.textContent || input.innerText;
        }
        return '';
    }

    /**
     * 清理测试输入框
     */
    cleanupTestInputs(inputs) {
        const container = document.getElementById('voice-clear-test-container');
        if (container) {
            container.remove();
        }
    }
}

// 导出测试套件
window.VoiceClearTestSuite = VoiceClearTestSuite;

// 自动运行测试（如果在测试环境中）
if (window.location.href.includes('voice_clear_demo.html')) {
    // 等待页面加载完成后运行测试
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(async () => {
            console.log('🚀 自动启动语音清除功能测试...');
            const testSuite = new VoiceClearTestSuite();
            await testSuite.runAllTests();
        }, 1000);
    });
}
